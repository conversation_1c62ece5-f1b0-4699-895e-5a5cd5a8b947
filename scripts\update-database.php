<?php
/**
 * سكريبت تحديث قاعدة البيانات
 * نظام ERP المحاسبي
 */

require_once __DIR__ . '/../config/database.php';

class DatabaseUpdater {
    private $db;
    private $currentVersion;
    private $targetVersion = '1.0.0';
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->currentVersion = $this->getCurrentVersion();
    }
    
    /**
     * تشغيل التحديثات
     */
    public function run() {
        echo "=================================\n";
        echo "  تحديث قاعدة البيانات\n";
        echo "=================================\n\n";
        
        echo "الإصدار الحالي: " . $this->currentVersion . "\n";
        echo "الإصدار المستهدف: " . $this->targetVersion . "\n\n";
        
        if (version_compare($this->currentVersion, $this->targetVersion, '>=')) {
            echo "قاعدة البيانات محدثة بالفعل.\n";
            return;
        }
        
        $updates = $this->getUpdates();
        
        foreach ($updates as $version => $update) {
            if (version_compare($this->currentVersion, $version, '<')) {
                echo "تطبيق التحديث $version...\n";
                
                try {
                    $this->db->beginTransaction();
                    
                    // تنفيذ التحديث
                    $update['callback']();
                    
                    // تحديث رقم الإصدار
                    $this->updateVersion($version);
                    
                    $this->db->commit();
                    
                    echo "✓ تم تطبيق التحديث $version بنجاح\n";
                    $this->currentVersion = $version;
                    
                } catch (Exception $e) {
                    $this->db->rollback();
                    echo "✗ فشل في تطبيق التحديث $version: " . $e->getMessage() . "\n";
                    break;
                }
            }
        }
        
        echo "\nتم الانتهاء من التحديثات.\n";
    }
    
    /**
     * الحصول على الإصدار الحالي
     */
    private function getCurrentVersion() {
        try {
            // إنشاء جدول الإصدارات إذا لم يكن موجوداً
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS system_versions (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    version VARCHAR(20) NOT NULL,
                    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
            
            $stmt = $this->db->query("SELECT version FROM system_versions ORDER BY applied_at DESC LIMIT 1");
            $result = $stmt->fetch();
            
            return $result ? $result['version'] : '0.0.0';
            
        } catch (Exception $e) {
            return '0.0.0';
        }
    }
    
    /**
     * تحديث رقم الإصدار
     */
    private function updateVersion($version) {
        $stmt = $this->db->prepare("INSERT INTO system_versions (version) VALUES (?)");
        $stmt->execute([$version]);
    }
    
    /**
     * قائمة التحديثات
     */
    private function getUpdates() {
        return [
            '1.0.0' => [
                'description' => 'الإصدار الأولي',
                'callback' => [$this, 'update_1_0_0']
            ],
            '1.0.1' => [
                'description' => 'إضافة فهارس للأداء',
                'callback' => [$this, 'update_1_0_1']
            ],
            '1.0.2' => [
                'description' => 'إضافة جداول التقارير',
                'callback' => [$this, 'update_1_0_2']
            ]
        ];
    }
    
    /**
     * تحديث 1.0.0 - الإصدار الأولي
     */
    private function update_1_0_0() {
        // التحقق من وجود الجداول الأساسية
        $tables = [
            'companies', 'branches', 'users', 'customers', 'suppliers',
            'products', 'invoices', 'purchase_orders', 'chart_of_accounts'
        ];
        
        foreach ($tables as $table) {
            $stmt = $this->db->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            
            if (!$stmt->fetch()) {
                throw new Exception("الجدول $table غير موجود. يرجى تشغيل install.php أولاً.");
            }
        }
        
        echo "  ✓ تم التحقق من الجداول الأساسية\n";
    }
    
    /**
     * تحديث 1.0.1 - إضافة فهارس للأداء
     */
    private function update_1_0_1() {
        $indexes = [
            "CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_date_range ON invoices(invoice_date, due_date)",
            "CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)",
            "CREATE INDEX IF NOT EXISTS idx_customers_active ON customers(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_active ON suppliers(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries(entry_date)",
            "CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date)"
        ];
        
        foreach ($indexes as $index) {
            $this->db->exec($index);
        }
        
        echo "  ✓ تم إضافة الفهارس للأداء\n";
    }
    
    /**
     * تحديث 1.0.2 - إضافة جداول التقارير
     */
    private function update_1_0_2() {
        // جدول التقارير المحفوظة
        $this->db->exec("
            CREATE TABLE IF NOT EXISTS saved_reports (
                id INT PRIMARY KEY AUTO_INCREMENT,
                company_id INT NOT NULL,
                user_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                type VARCHAR(100) NOT NULL,
                parameters JSON,
                is_public BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
        
        // جدول جدولة التقارير
        $this->db->exec("
            CREATE TABLE IF NOT EXISTS report_schedules (
                id INT PRIMARY KEY AUTO_INCREMENT,
                company_id INT NOT NULL,
                user_id INT NOT NULL,
                report_id INT NOT NULL,
                frequency ENUM('daily', 'weekly', 'monthly', 'yearly') NOT NULL,
                next_run DATETIME NOT NULL,
                last_run DATETIME NULL,
                is_active BOOLEAN DEFAULT TRUE,
                email_recipients TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (report_id) REFERENCES saved_reports(id) ON DELETE CASCADE
            )
        ");
        
        // جدول سجل التقارير
        $this->db->exec("
            CREATE TABLE IF NOT EXISTS report_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                company_id INT NOT NULL,
                user_id INT,
                report_type VARCHAR(100) NOT NULL,
                parameters JSON,
                execution_time DECIMAL(8,3),
                status ENUM('success', 'error') NOT NULL,
                error_message TEXT,
                file_path VARCHAR(500),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            )
        ");
        
        echo "  ✓ تم إضافة جداول التقارير\n";
    }
}

// تشغيل التحديث
try {
    $updater = new DatabaseUpdater();
    $updater->run();
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
    exit(1);
}
?>
