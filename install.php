<?php
/**
 * ملف التثبيت التلقائي
 * نظام ERP المحاسبي
 */

// التحقق من وجود ملف الإعدادات
if (file_exists('config/database.php')) {
    require_once 'config/database.php';
    
    // التحقق من وجود الجداول
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        $stmt = $db->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            header('Location: index.php');
            exit('النظام مثبت بالفعل. <a href="index.php">انتقل للصفحة الرئيسية</a>');
        }
    } catch (Exception $e) {
        // قاعدة البيانات غير موجودة أو غير متصلة
    }
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    switch ($step) {
        case 1:
            // التحقق من المتطلبات
            $step = 2;
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $host = $_POST['host'] ?? 'localhost';
            $dbname = $_POST['dbname'] ?? '';
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            
            if (empty($dbname) || empty($username)) {
                $error = 'يرجى إدخال جميع البيانات المطلوبة';
            } else {
                try {
                    // اختبار الاتصال
                    $pdo = new PDO("mysql:host=$host", $username, $password);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // إنشاء قاعدة البيانات
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    $pdo->exec("USE `$dbname`");
                    
                    // قراءة وتنفيذ ملف SQL
                    $sql = file_get_contents('sql/database_structure.sql');
                    $sql = str_replace('CREATE DATABASE IF NOT EXISTS erp_accounting CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;', '', $sql);
                    $sql = str_replace('USE erp_accounting;', '', $sql);
                    
                    // تقسيم الاستعلامات
                    $queries = explode(';', $sql);
                    foreach ($queries as $query) {
                        $query = trim($query);
                        if (!empty($query) && !preg_match('/^--/', $query)) {
                            $pdo->exec($query);
                        }
                    }
                    
                    // إنشاء ملف الإعدادات
                    $configContent = "<?php\n";
                    $configContent .= "define('DB_HOST', '$host');\n";
                    $configContent .= "define('DB_NAME', '$dbname');\n";
                    $configContent .= "define('DB_USER', '$username');\n";
                    $configContent .= "define('DB_PASS', '$password');\n";
                    $configContent .= "define('DB_CHARSET', 'utf8mb4');\n\n";
                    $configContent .= "define('SITE_URL', 'http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/');\n";
                    $configContent .= "define('SITE_NAME', 'نظام ERP المحاسبي');\n";
                    $configContent .= "define('CURRENCY', 'ر.س');\n";
                    $configContent .= "define('DATE_FORMAT', 'Y-m-d');\n";
                    $configContent .= "define('DATETIME_FORMAT', 'Y-m-d H:i:s');\n\n";
                    $configContent .= "define('SESSION_TIMEOUT', 3600);\n";
                    $configContent .= "define('PASSWORD_MIN_LENGTH', 6);\n";
                    $configContent .= "define('MAX_LOGIN_ATTEMPTS', 5);\n\n";
                    $configContent .= file_get_contents('config/database.php');
                    
                    if (!is_dir('config')) {
                        mkdir('config', 0755, true);
                    }
                    
                    file_put_contents('config/database.php', $configContent);
                    
                    $step = 3;
                    $success = 'تم إنشاء قاعدة البيانات بنجاح';
                    
                } catch (Exception $e) {
                    $error = 'خطأ في إنشاء قاعدة البيانات: ' . $e->getMessage();
                }
            }
            break;
            
        case 3:
            // إعداد المدير
            $name = $_POST['name'] ?? '';
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            if (empty($name) || empty($email) || empty($password)) {
                $error = 'يرجى إدخال جميع البيانات المطلوبة';
            } elseif ($password !== $confirm_password) {
                $error = 'كلمة المرور غير متطابقة';
            } elseif (strlen($password) < 6) {
                $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
            } else {
                try {
                    require_once 'config/database.php';
                    $db = new Database();
                    $conn = $db->getConnection();
                    
                    // تحديث بيانات المدير
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $conn->prepare("UPDATE users SET name = ?, email = ?, password = ? WHERE id = 1");
                    $stmt->execute([$name, $email, $hashedPassword]);
                    
                    $step = 4;
                    $success = 'تم إعداد حساب المدير بنجاح';
                    
                } catch (Exception $e) {
                    $error = 'خطأ في إعداد حساب المدير: ' . $e->getMessage();
                }
            }
            break;
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .install-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .install-header {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e3e6f0;
            color: #5a5c69;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        
        .step.active {
            background: #4e73df;
            color: white;
        }
        
        .step.completed {
            background: #1cc88a;
            color: white;
        }
        
        .requirements-list {
            list-style: none;
            padding: 0;
        }
        
        .requirements-list li {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            display: flex;
            align-items: center;
        }
        
        .requirements-list .success {
            background: #d1edff;
            color: #0c5460;
        }
        
        .requirements-list .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <i class="fas fa-cog fa-3x mb-3"></i>
            <h2>تثبيت نظام ERP المحاسبي</h2>
            <p class="mb-0">مرحباً بك في معالج التثبيت</p>
        </div>
        
        <div class="install-body">
            <!-- مؤشر الخطوات -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? 'active' : ''; ?>">4</div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <!-- الخطوة 1: التحقق من المتطلبات -->
                <h4>التحقق من متطلبات النظام</h4>
                <ul class="requirements-list">
                    <li class="<?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'success' : 'error'; ?>">
                        <i class="fas <?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'fa-check' : 'fa-times'; ?> me-2"></i>
                        PHP 7.4+ (الحالي: <?php echo PHP_VERSION; ?>)
                    </li>
                    <li class="<?php echo extension_loaded('pdo') ? 'success' : 'error'; ?>">
                        <i class="fas <?php echo extension_loaded('pdo') ? 'fa-check' : 'fa-times'; ?> me-2"></i>
                        PDO Extension
                    </li>
                    <li class="<?php echo extension_loaded('pdo_mysql') ? 'success' : 'error'; ?>">
                        <i class="fas <?php echo extension_loaded('pdo_mysql') ? 'fa-check' : 'fa-times'; ?> me-2"></i>
                        PDO MySQL Extension
                    </li>
                    <li class="<?php echo is_writable('.') ? 'success' : 'error'; ?>">
                        <i class="fas <?php echo is_writable('.') ? 'fa-check' : 'fa-times'; ?> me-2"></i>
                        صلاحيات الكتابة
                    </li>
                </ul>
                
                <form method="POST" action="?step=2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-arrow-left me-2"></i>
                        التالي
                    </button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <!-- الخطوة 2: إعداد قاعدة البيانات -->
                <h4>إعداد قاعدة البيانات</h4>
                <form method="POST" action="?step=2">
                    <div class="mb-3">
                        <label class="form-label">خادم قاعدة البيانات</label>
                        <input type="text" class="form-control" name="host" value="localhost" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" class="form-control" name="dbname" value="erp_accounting" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" name="password">
                    </div>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-database me-2"></i>
                        إنشاء قاعدة البيانات
                    </button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <!-- الخطوة 3: إعداد المدير -->
                <h4>إعداد حساب المدير</h4>
                <form method="POST" action="?step=3">
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" class="form-control" name="confirm_password" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-user-plus me-2"></i>
                        إنشاء حساب المدير
                    </button>
                </form>
                
            <?php elseif ($step == 4): ?>
                <!-- الخطوة 4: اكتمال التثبيت -->
                <div class="text-center">
                    <i class="fas fa-check-circle fa-5x text-success mb-4"></i>
                    <h4>تم التثبيت بنجاح!</h4>
                    <p class="text-muted mb-4">تم تثبيت نظام ERP المحاسبي بنجاح. يمكنك الآن البدء في استخدام النظام.</p>
                    
                    <div class="alert alert-warning">
                        <strong>مهم:</strong> يرجى حذف ملف install.php من الخادم لأسباب أمنية.
                    </div>
                    
                    <a href="index.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>
                        الذهاب للصفحة الرئيسية
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
