<?php
/**
 * الدوال المساعدة العامة
 * نظام ERP المحاسبي
 */

/**
 * التحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * التحقق من الصلاحيات
 */
function hasPermission($permission) {
    if (!isLoggedIn()) {
        return false;
    }
    
    // التحقق من صلاحيات المستخدم
    $userPermissions = $_SESSION['permissions'] ?? [];
    return in_array($permission, $userPermissions) || in_array('admin', $userPermissions);
}

/**
 * إعادة توجيه المستخدم
 */
function redirect($url) {
    header("Location: $url");
    exit();
}

/**
 * عرض رسالة تنبيه
 */
function setAlert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * جلب وعرض رسالة التنبيه
 */
function getAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        return $alert;
    }
    return null;
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من صحة رقم الهاتف
 */
function isValidPhone($phone) {
    return preg_match('/^[0-9+\-\s()]+$/', $phone);
}

/**
 * تنظيف النص
 */
function cleanText($text) {
    return htmlspecialchars(trim($text), ENT_QUOTES, 'UTF-8');
}

/**
 * تحويل التاريخ إلى التنسيق العربي
 */
function formatArabicDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}

/**
 * تحويل الأرقام إلى العربية
 */
function toArabicNumbers($number) {
    $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($english, $arabic, $number);
}

/**
 * تحويل الأرقام إلى الإنجليزية
 */
function toEnglishNumbers($number) {
    $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($arabic, $english, $number);
}

/**
 * تحويل المبلغ إلى كلمات عربية
 */
function numberToArabicWords($number) {
    $ones = [
        '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
        'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر',
        'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
    ];
    
    $tens = [
        '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
    ];
    
    $hundreds = [
        '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
    ];
    
    if ($number == 0) return 'صفر';
    
    $result = '';
    
    // معالجة الآلاف
    if ($number >= 1000) {
        $thousands = intval($number / 1000);
        if ($thousands == 1) {
            $result .= 'ألف ';
        } elseif ($thousands == 2) {
            $result .= 'ألفان ';
        } elseif ($thousands <= 10) {
            $result .= $ones[$thousands] . ' آلاف ';
        } else {
            $result .= convertHundreds($thousands) . ' ألف ';
        }
        $number = $number % 1000;
    }
    
    // معالجة المئات
    $result .= convertHundreds($number);
    
    return trim($result);
}

/**
 * دالة مساعدة لتحويل المئات
 */
function convertHundreds($number) {
    $ones = [
        '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
        'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر',
        'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
    ];
    
    $tens = [
        '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
    ];
    
    $hundreds = [
        '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
    ];
    
    $result = '';
    
    if ($number >= 100) {
        $result .= $hundreds[intval($number / 100)] . ' ';
        $number = $number % 100;
    }
    
    if ($number >= 20) {
        $result .= $tens[intval($number / 10)];
        if ($number % 10 > 0) {
            $result .= ' ' . $ones[$number % 10];
        }
    } elseif ($number > 0) {
        $result .= $ones[$number];
    }
    
    return trim($result);
}

/**
 * تسجيل العمليات في السجل
 */
function logActivity($action, $details = '', $user_id = null) {
    global $db;
    
    if (!$user_id && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }
    
    $sql = "INSERT INTO activity_log (user_id, action, details, ip_address, created_at) 
            VALUES (?, ?, ?, ?, NOW())";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        $user_id,
        $action,
        $details,
        $_SERVER['REMOTE_ADDR'] ?? ''
    ]);
}

/**
 * إنشاء رقم مرجعي فريد
 */
function generateReference($prefix = '', $length = 8) {
    $reference = $prefix . date('Ymd') . str_pad(mt_rand(1, 99999999), $length, '0', STR_PAD_LEFT);
    return $reference;
}

/**
 * التحقق من صحة الرقم الضريبي
 */
function isValidTaxNumber($taxNumber) {
    // التحقق من الرقم الضريبي السعودي (15 رقم)
    return preg_match('/^[0-9]{15}$/', $taxNumber);
}

/**
 * حساب الضريبة
 */
function calculateTax($amount, $taxRate = 15) {
    return ($amount * $taxRate) / 100;
}

/**
 * حساب المبلغ شامل الضريبة
 */
function calculateTotalWithTax($amount, $taxRate = 15) {
    return $amount + calculateTax($amount, $taxRate);
}

/**
 * تحويل الملف إلى Base64
 */
function fileToBase64($filePath) {
    if (file_exists($filePath)) {
        $fileData = file_get_contents($filePath);
        return base64_encode($fileData);
    }
    return false;
}

/**
 * حفظ ملف من Base64
 */
function base64ToFile($base64Data, $filePath) {
    $fileData = base64_decode($base64Data);
    return file_put_contents($filePath, $fileData);
}

/**
 * التحقق من نوع الملف المسموح
 */
function isAllowedFileType($fileName, $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx']) {
    $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    return in_array($extension, $allowedTypes);
}

/**
 * تحويل حجم الملف إلى تنسيق قابل للقراءة
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * إرسال بريد إلكتروني
 */
function sendEmail($to, $subject, $message, $from = null) {
    if (!$from) {
        $from = 'noreply@' . $_SERVER['HTTP_HOST'];
    }
    
    $headers = [
        'From: ' . $from,
        'Reply-To: ' . $from,
        'Content-Type: text/html; charset=UTF-8',
        'MIME-Version: 1.0'
    ];
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * إنشاء كود QR
 */
function generateQRCode($data, $size = 200) {
    $url = "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data=" . urlencode($data);
    return $url;
}
?>
