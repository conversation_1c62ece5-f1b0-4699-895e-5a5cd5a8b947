{"name": "erp-accounting-frontend", "version": "1.0.0", "description": "Frontend assets for ERP Accounting System", "main": "assets/js/app.js", "scripts": {"build": "npm run build-css && npm run build-js", "build-css": "sass assets/scss/main.scss assets/css/compiled.css --style compressed", "build-js": "webpack --mode production", "dev": "npm run dev-css && npm run dev-js", "dev-css": "sass assets/scss/main.scss assets/css/compiled.css --watch", "dev-js": "webpack --mode development --watch", "lint": "eslint assets/js/**/*.js", "lint-fix": "eslint assets/js/**/*.js --fix", "test": "jest", "serve": "browser-sync start --server --files '**/*.php, **/*.css, **/*.js'", "optimize-images": "imagemin assets/images/**/* --out-dir=assets/images/optimized", "clean": "rimraf assets/css/compiled.css assets/js/dist/"}, "keywords": ["erp", "accounting", "arabic", "frontend", "bootstrap", "javascript"], "author": "ERP Accounting Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "babel-loader": "^9.1.0", "browser-sync": "^2.29.0", "css-loader": "^6.8.0", "eslint": "^8.42.0", "eslint-config-standard": "^17.1.0", "imagemin": "^8.0.1", "imagemin-cli": "^7.0.0", "jest": "^29.5.0", "mini-css-extract-plugin": "^2.7.0", "rimraf": "^5.0.0", "sass": "^1.63.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "dependencies": {"bootstrap": "^5.3.0", "chart.js": "^4.3.0", "datatables.net": "^1.13.0", "datatables.net-bs5": "^1.13.0", "jquery": "^3.7.0", "moment": "^2.29.0", "select2": "^4.1.0", "sweetalert2": "^11.7.0", "toastr": "^2.1.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["**/tests/**/*.test.js"]}, "eslintConfig": {"extends": ["standard"], "env": {"browser": true, "jquery": true, "es6": true}, "globals": {"Chart": "readonly", "bootstrap": "readonly", "Swal": "readonly", "toastr": "readonly"}}, "repository": {"type": "git", "url": "https://github.com/your-repo/erp-accounting.git"}, "bugs": {"url": "https://github.com/your-repo/erp-accounting/issues"}, "homepage": "https://erp-accounting.com"}