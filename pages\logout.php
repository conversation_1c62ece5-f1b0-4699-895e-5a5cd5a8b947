<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// تسجيل العملية قبل تسجيل الخروج
if (isset($_SESSION['user_id'])) {
    logActivity('تسجيل خروج', 'تم تسجيل الخروج بنجاح', $_SESSION['user_id']);
    
    // حذف remember token إذا كان موجود
    if (isset($_COOKIE['remember_token'])) {
        $sql = "UPDATE users SET remember_token = NULL WHERE id = ?";
        $database->query($sql, [$_SESSION['user_id']]);
        
        // حذف cookie
        setcookie('remember_token', '', time() - 3600, '/');
    }
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه لصفحة تسجيل الدخول
redirect('login.php');
?>
