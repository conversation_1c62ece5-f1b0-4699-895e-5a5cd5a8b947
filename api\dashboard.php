<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح بالوصول']);
    exit();
}

try {
    $response = [];
    $company_id = $_SESSION['company_id'] ?? 1;
    
    // إحصائيات المبيعات الشهرية
    $currentMonth = date('Y-m');
    $salesSql = "SELECT COALESCE(SUM(total_amount), 0) as monthly_sales 
                 FROM invoices 
                 WHERE company_id = ? 
                 AND DATE_FORMAT(created_at, '%Y-%m') = ? 
                 AND status = 'paid'";
    $salesResult = $database->fetch($salesSql, [$company_id, $currentMonth]);
    $response['monthly_sales'] = $salesResult['monthly_sales'] ?? 0;
    
    // عدد الفواتير
    $invoicesSql = "SELECT COUNT(*) as invoice_count 
                    FROM invoices 
                    WHERE company_id = ? 
                    AND DATE_FORMAT(created_at, '%Y-%m') = ?";
    $invoicesResult = $database->fetch($invoicesSql, [$company_id, $currentMonth]);
    $response['invoice_count'] = $invoicesResult['invoice_count'] ?? 0;
    
    // عدد العملاء
    $customersSql = "SELECT COUNT(*) as customer_count 
                     FROM customers 
                     WHERE company_id = ? 
                     AND is_active = 1";
    $customersResult = $database->fetch($customersSql, [$company_id]);
    $response['customer_count'] = $customersResult['customer_count'] ?? 0;
    
    // عدد المنتجات
    $productsSql = "SELECT COUNT(*) as product_count 
                    FROM products 
                    WHERE company_id = ? 
                    AND is_active = 1";
    $productsResult = $database->fetch($productsSql, [$company_id]);
    $response['product_count'] = $productsResult['product_count'] ?? 0;
    
    // مبيعات الأشهر الستة الأخيرة
    $salesChartSql = "SELECT 
                        DATE_FORMAT(created_at, '%Y-%m') as month,
                        COALESCE(SUM(total_amount), 0) as total_sales
                      FROM invoices 
                      WHERE company_id = ? 
                      AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
                      AND status = 'paid'
                      GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                      ORDER BY month ASC";
    $salesChartData = $database->fetchAll($salesChartSql, [$company_id]);
    
    // تنسيق بيانات الرسم البياني
    $chartLabels = [];
    $chartData = [];
    $months = [
        '01' => 'يناير', '02' => 'فبراير', '03' => 'مارس',
        '04' => 'أبريل', '05' => 'مايو', '06' => 'يونيو',
        '07' => 'يوليو', '08' => 'أغسطس', '09' => 'سبتمبر',
        '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
    ];
    
    foreach ($salesChartData as $row) {
        $monthNum = substr($row['month'], -2);
        $chartLabels[] = $months[$monthNum];
        $chartData[] = floatval($row['total_sales']);
    }
    
    $response['sales_chart'] = [
        'labels' => $chartLabels,
        'data' => $chartData
    ];
    
    // أحدث الفواتير
    $recentInvoicesSql = "SELECT 
                            i.invoice_number,
                            c.name as customer_name,
                            i.total_amount,
                            i.created_at
                          FROM invoices i
                          JOIN customers c ON i.customer_id = c.id
                          WHERE i.company_id = ?
                          ORDER BY i.created_at DESC
                          LIMIT 5";
    $recentInvoices = $database->fetchAll($recentInvoicesSql, [$company_id]);
    
    // تنسيق الفواتير
    $formattedInvoices = [];
    foreach ($recentInvoices as $invoice) {
        $formattedInvoices[] = [
            'invoice_number' => $invoice['invoice_number'],
            'customer_name' => $invoice['customer_name'],
            'total_amount' => formatCurrency($invoice['total_amount']),
            'created_at' => formatArabicDate($invoice['created_at'])
        ];
    }
    
    $response['recent_invoices'] = $formattedInvoices;
    
    // المنتجات منخفضة المخزون
    $lowStockSql = "SELECT 
                      p.name,
                      p.current_stock,
                      p.min_stock_level
                    FROM products p
                    WHERE p.company_id = ?
                    AND p.current_stock <= p.min_stock_level
                    AND p.is_active = 1
                    ORDER BY (p.current_stock / p.min_stock_level) ASC
                    LIMIT 5";
    $lowStockProducts = $database->fetchAll($lowStockSql, [$company_id]);
    $response['low_stock_products'] = $lowStockProducts;
    
    // الفواتير المتأخرة
    $overdueSql = "SELECT COUNT(*) as overdue_count
                   FROM invoices
                   WHERE company_id = ?
                   AND status = 'pending'
                   AND due_date < CURDATE()";
    $overdueResult = $database->fetch($overdueSql, [$company_id]);
    $response['overdue_invoices'] = $overdueResult['overdue_count'] ?? 0;
    
    // إجمالي المدينين
    $receivablesSql = "SELECT COALESCE(SUM(total_amount), 0) as total_receivables
                       FROM invoices
                       WHERE company_id = ?
                       AND status IN ('pending', 'partial')";
    $receivablesResult = $database->fetch($receivablesSql, [$company_id]);
    $response['total_receivables'] = $receivablesResult['total_receivables'] ?? 0;
    
    // إجمالي الدائنين
    $payablesSql = "SELECT COALESCE(SUM(total_amount), 0) as total_payables
                    FROM purchase_orders
                    WHERE company_id = ?
                    AND status IN ('pending', 'partial')";
    $payablesResult = $database->fetch($payablesSql, [$company_id]);
    $response['total_payables'] = $payablesResult['total_payables'] ?? 0;
    
    // الأنشطة الأخيرة
    $activitiesSql = "SELECT 
                        al.action,
                        al.details,
                        al.created_at,
                        u.name as user_name
                      FROM activity_log al
                      JOIN users u ON al.user_id = u.id
                      WHERE u.company_id = ?
                      ORDER BY al.created_at DESC
                      LIMIT 10";
    $activities = $database->fetchAll($activitiesSql, [$company_id]);
    
    // تنسيق الأنشطة
    $formattedActivities = [];
    foreach ($activities as $activity) {
        $formattedActivities[] = [
            'action' => $activity['action'],
            'details' => $activity['details'],
            'user_name' => $activity['user_name'],
            'created_at' => formatArabicDate($activity['created_at'])
        ];
    }
    
    $response['recent_activities'] = $formattedActivities;
    
    // معدل النمو الشهري
    $lastMonth = date('Y-m', strtotime('-1 month'));
    $lastMonthSalesSql = "SELECT COALESCE(SUM(total_amount), 0) as last_month_sales 
                          FROM invoices 
                          WHERE company_id = ? 
                          AND DATE_FORMAT(created_at, '%Y-%m') = ? 
                          AND status = 'paid'";
    $lastMonthResult = $database->fetch($lastMonthSalesSql, [$company_id, $lastMonth]);
    $lastMonthSales = $lastMonthResult['last_month_sales'] ?? 0;
    
    if ($lastMonthSales > 0) {
        $growthRate = (($response['monthly_sales'] - $lastMonthSales) / $lastMonthSales) * 100;
        $response['growth_rate'] = round($growthRate, 2);
    } else {
        $response['growth_rate'] = 0;
    }
    
    // حالة النظام
    $response['system_status'] = [
        'database_status' => 'متصل',
        'last_backup' => '2024-01-15 10:30:00',
        'disk_usage' => '45%',
        'memory_usage' => '62%'
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في الخادم',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
