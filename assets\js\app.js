/**
 * ملف JavaScript الرئيسي
 * نظام ERP المحاسبي
 */

// متغيرات عامة
let currentPage = 'dashboard';
let loadingOverlay = null;

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadDashboardData();
    setupEventListeners();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // إنشاء overlay التحميل
    createLoadingOverlay();
    
    // تحديث الوقت كل ثانية
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // تحميل التنبيهات
    loadAlerts();
    
    console.log('تم تهيئة نظام ERP المحاسبي');
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // روابط الشريط الجانبي
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const href = this.getAttribute('href');
            if (href && href.startsWith('#')) {
                const page = href.substring(1);
                loadPage(page);
                setActiveNavLink(this);
            }
        });
    });
    
    // روابط القائمة المنسدلة
    document.querySelectorAll('.dropdown-item').forEach(item => {
        item.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href && href.startsWith('#')) {
                e.preventDefault();
                const page = href.substring(1);
                loadPage(page);
            }
        });
    });
}

/**
 * تحميل صفحة معينة
 */
function loadPage(page) {
    if (page === currentPage) return;
    
    showLoading();
    currentPage = page;
    
    // محاكاة تحميل الصفحة
    setTimeout(() => {
        switch(page) {
            case 'dashboard':
                loadDashboard();
                break;
            case 'companies':
                loadCompanies();
                break;
            case 'accounting':
                loadAccounting();
                break;
            case 'sales':
                loadSales();
                break;
            case 'purchases':
                loadPurchases();
                break;
            case 'inventory':
                loadInventory();
                break;
            case 'reports':
                loadReports();
                break;
            case 'settings':
                loadSettings();
                break;
            default:
                loadDashboard();
        }
        hideLoading();
    }, 500);
}

/**
 * تحميل لوحة التحكم
 */
function loadDashboard() {
    const content = `
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">لوحة التحكم</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary">تصدير</button>
                </div>
                <button type="button" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    جديد
                </button>
            </div>
        </div>
        
        <!-- بطاقات الإحصائيات -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-right-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col me-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي المبيعات (شهري)
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">125,450 ر.س</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-right-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col me-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    عدد الفواتير
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">156</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-right-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col me-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    عدد العملاء
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">89</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-right-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col me-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    عدد المنتجات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">245</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-box fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الرسوم البيانية والجداول -->
        <div class="row">
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">مبيعات الأشهر الأخيرة</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="salesChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">أحدث الفواتير</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>INV-001</td>
                                        <td>شركة الأمل للتجارة</td>
                                        <td>5,250 ر.س</td>
                                        <td>2024-01-15</td>
                                    </tr>
                                    <tr>
                                        <td>INV-002</td>
                                        <td>مؤسسة النور</td>
                                        <td>3,800 ر.س</td>
                                        <td>2024-01-14</td>
                                    </tr>
                                    <tr>
                                        <td>INV-003</td>
                                        <td>شركة المستقبل</td>
                                        <td>7,920 ر.س</td>
                                        <td>2024-01-13</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('main-content').innerHTML = content;
    
    // رسم الرسم البياني
    drawSalesChart();
}

/**
 * رسم الرسم البياني للمبيعات
 */
function drawSalesChart() {
    const ctx = document.getElementById('salesChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'المبيعات',
                data: [65000, 78000, 85000, 92000, 105000, 125000],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ر.س';
                        }
                    }
                }
            }
        }
    });
}

/**
 * تحميل صفحة الشركات
 */
function loadCompanies() {
    const content = `
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">إدارة الشركات والفروع</h1>
            <button type="button" class="btn btn-primary" onclick="showAddCompanyModal()">
                <i class="fas fa-plus me-1"></i>
                إضافة شركة جديدة
            </button>
        </div>
        
        <div class="card shadow">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>اسم الشركة</th>
                                <th>الرقم الضريبي</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>عدد الفروع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>الشركة الرئيسية</td>
                                <td>123456789012345</td>
                                <td>+966501234567</td>
                                <td><EMAIL></td>
                                <td>3</td>
                                <td>
                                    <button class="btn btn-sm btn-primary me-1">تعديل</button>
                                    <button class="btn btn-sm btn-info me-1">الفروع</button>
                                    <button class="btn btn-sm btn-danger">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('main-content').innerHTML = content;
}

/**
 * تحميل بيانات لوحة التحكم
 */
function loadDashboardData() {
    // محاكاة تحميل البيانات من الخادم
    fetch('api/dashboard.php')
        .then(response => response.json())
        .then(data => {
            updateDashboardStats(data);
        })
        .catch(error => {
            console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
        });
}

/**
 * تحديث إحصائيات لوحة التحكم
 */
function updateDashboardStats(data) {
    if (data.monthly_sales) {
        document.getElementById('monthly-sales').textContent = formatCurrency(data.monthly_sales);
    }
    if (data.invoice_count) {
        document.getElementById('invoice-count').textContent = data.invoice_count;
    }
    if (data.customer_count) {
        document.getElementById('customer-count').textContent = data.customer_count;
    }
    if (data.product_count) {
        document.getElementById('product-count').textContent = data.product_count;
    }
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

/**
 * تحديث التاريخ والوقت
 */
function updateDateTime() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'Asia/Riyadh'
    };
    
    const dateTimeString = now.toLocaleDateString('ar-SA', options);
    const dateTimeElement = document.getElementById('current-datetime');
    if (dateTimeElement) {
        dateTimeElement.textContent = dateTimeString;
    }
}

/**
 * إظهار overlay التحميل
 */
function showLoading() {
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }
}

/**
 * إخفاء overlay التحميل
 */
function hideLoading() {
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

/**
 * إنشاء overlay التحميل
 */
function createLoadingOverlay() {
    loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.style.display = 'none';
    loadingOverlay.innerHTML = `
        <div class="text-center text-white">
            <div class="spinner-border mb-3" role="status">
                <span class="sr-only">جاري التحميل...</span>
            </div>
            <div>جاري التحميل...</div>
        </div>
    `;
    document.body.appendChild(loadingOverlay);
}

/**
 * تعيين الرابط النشط في الشريط الجانبي
 */
function setActiveNavLink(activeLink) {
    // إزالة الفئة النشطة من جميع الروابط
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // إضافة الفئة النشطة للرابط المحدد
    activeLink.classList.add('active');
}

/**
 * تحميل التنبيهات
 */
function loadAlerts() {
    const alertContainer = document.getElementById('alert-container');
    if (alertContainer) {
        // محاكاة تحميل التنبيهات
        const alerts = [
            { type: 'warning', message: 'يوجد 3 فواتير متأخرة السداد' },
            { type: 'info', message: 'تم إضافة منتج جديد بنجاح' }
        ];
        
        alerts.forEach(alert => {
            showAlert(alert.message, alert.type);
        });
    }
}

/**
 * عرض تنبيه
 */
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}

// دوال إضافية للصفحات الأخرى
function loadAccounting() {
    document.getElementById('main-content').innerHTML = '<h2>صفحة المحاسبة قيد التطوير</h2>';
}

function loadSales() {
    document.getElementById('main-content').innerHTML = '<h2>صفحة المبيعات قيد التطوير</h2>';
}

function loadPurchases() {
    document.getElementById('main-content').innerHTML = '<h2>صفحة المشتريات قيد التطوير</h2>';
}

function loadInventory() {
    document.getElementById('main-content').innerHTML = '<h2>صفحة المخزون قيد التطوير</h2>';
}

function loadReports() {
    document.getElementById('main-content').innerHTML = '<h2>صفحة التقارير قيد التطوير</h2>';
}

function loadSettings() {
    document.getElementById('main-content').innerHTML = '<h2>صفحة الإعدادات قيد التطوير</h2>';
}
