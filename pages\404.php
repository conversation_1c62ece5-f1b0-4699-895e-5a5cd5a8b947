<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - نظام ERP المحاسبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .error-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 2rem;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .error-description {
            font-size: 1.1rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .btn-home {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .btn-home:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .floating-icon {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .icon-1 { top: 10%; left: 10%; animation-delay: 0s; }
        .icon-2 { top: 20%; right: 10%; animation-delay: 2s; }
        .icon-3 { bottom: 20%; left: 15%; animation-delay: 4s; }
        .icon-4 { bottom: 10%; right: 20%; animation-delay: 1s; }
    </style>
</head>
<body>
    <!-- أيقونات عائمة -->
    <i class="fas fa-chart-line fa-3x floating-icon icon-1"></i>
    <i class="fas fa-calculator fa-2x floating-icon icon-2"></i>
    <i class="fas fa-file-invoice fa-2x floating-icon icon-3"></i>
    <i class="fas fa-building fa-3x floating-icon icon-4"></i>
    
    <div class="error-container">
        <div class="error-code">404</div>
        <div class="error-message">الصفحة غير موجودة</div>
        <div class="error-description">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
            <br>
            يمكنك العودة إلى الصفحة الرئيسية أو التحقق من الرابط.
        </div>
        
        <a href="../index.php" class="btn-home">
            <i class="fas fa-home me-2"></i>
            العودة للصفحة الرئيسية
        </a>
        
        <div class="mt-4">
            <small style="opacity: 0.7;">
                إذا كنت تعتقد أن هذا خطأ، يرجى الاتصال بالدعم الفني
            </small>
        </div>
    </div>
    
    <script>
        // تأثير حركي للصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.error-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.8s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 200);
        });
    </script>
</body>
</html>
