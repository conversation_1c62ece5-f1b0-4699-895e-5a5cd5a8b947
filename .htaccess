# نظام ERP المحاسبي - إعدادات Apache

# تفعيل mod_rewrite
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "config/database.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول للمجلدات الحساسة
RedirectMatch 403 ^/config/
RedirectMatch 403 ^/includes/
RedirectMatch 403 ^/sql/
RedirectMatch 403 ^/logs/

# إعدادات الأمان
ServerSignature Off

# منع عرض محتويات المجلدات
Options -Indexes

# حماية من XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self';"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# إعدادات PHP
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value memory_limit 256M
    php_value session.gc_maxlifetime 3600
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
</IfModule>

# منع الوصول للملفات المخفية
<Files ".*">
    Order Allow,Deny
    Deny from all
</Files>

# السماح بالوصول لملفات معينة
<Files ".well-known">
    Order Allow,Deny
    Allow from all
</Files>

# إعادة توجيه الأخطاء
ErrorDocument 404 /pages/404.php
ErrorDocument 403 /pages/403.php
ErrorDocument 500 /pages/500.php

# منع الوصول المباشر للملفات PHP في مجلدات معينة
<FilesMatch "\.(php|phtml)$">
    <If "%{REQUEST_URI} =~ m#^/(config|includes|sql)/#">
        Require all denied
    </If>
</FilesMatch>

# حماية من SQL Injection
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# منع User Agents المشبوهة
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} ^.*(libwww-perl|curl|wget|python|nikto|scan) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} ^.*(<|>|'|%0A|%0D|%27|%3C|%3E|%00) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# حماية من Hotlinking
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|css|js)$ - [F]
</IfModule>

# تحديد نوع المحتوى للملفات العربية
AddDefaultCharset UTF-8
AddCharset UTF-8 .php .html .css .js

# إعدادات إضافية للأمان
<IfModule mod_headers.c>
    Header unset Server
    Header unset X-Powered-By
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    Header set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# منع تنفيذ PHP في مجلد الرفع
<Directory "uploads">
    <Files "*.php">
        Order Allow,Deny
        Deny from all
    </Files>
</Directory>
