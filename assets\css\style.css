/* 
 * ملف التصميم الرئيسي
 * نظام ERP المحاسبي
 */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
    text-align: right;
}

/* تخصيص Bootstrap للعربية */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    font-weight: 600;
}

.sidebar .nav-link i {
    margin-left: 0.5rem;
    width: 16px;
    text-align: center;
}

/* المحتوى الرئيسي */
main {
    margin-right: 240px;
    padding-top: 56px;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 56px;
        width: 100%;
        height: auto;
        position: relative;
    }
    
    main {
        margin-right: 0;
    }
}

/* البطاقات */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
}

/* الألوان المخصصة */
.border-right-primary {
    border-right: 0.25rem solid #4e73df !important;
}

.border-right-success {
    border-right: 0.25rem solid #1cc88a !important;
}

.border-right-info {
    border-right: 0.25rem solid #36b9cc !important;
}

.border-right-warning {
    border-right: 0.25rem solid #f6c23e !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* الجداول */
.table {
    font-size: 0.9rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fc;
    color: #5a5c69;
}

.table-responsive {
    border-radius: 0.375rem;
}

/* الأزرار */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
    transform: translateY(-1px);
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-success:hover {
    background-color: #17a673;
    border-color: #169b6b;
    transform: translateY(-1px);
}

.btn-danger {
    background-color: #e74a3b;
    border-color: #e74a3b;
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #b53e2c;
    transform: translateY(-1px);
}

.btn-warning {
    background-color: #f6c23e;
    border-color: #f6c23e;
    color: #5a5c69;
}

.btn-warning:hover {
    background-color: #f4b619;
    border-color: #f4b619;
    color: #5a5c69;
    transform: translateY(-1px);
}

/* النماذج */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #d1d3e2;
    font-size: 0.9rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-select {
    border-radius: 0.375rem;
    border: 1px solid #d1d3e2;
    font-size: 0.9rem;
    padding: 0.75rem 1rem;
}

/* التنبيهات */
.alert {
    border-radius: 0.75rem;
    border: none;
    font-weight: 500;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* الشارات */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* التحميل */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* تخصيصات إضافية */
.text-xs {
    font-size: 0.75rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-uppercase {
    text-transform: uppercase;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.h-100 {
    height: 100% !important;
}

.py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}

.py-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.me-2 {
    margin-left: 0.5rem !important;
}

.me-auto {
    margin-left: auto !important;
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    main {
        margin-right: 0 !important;
        padding-top: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    body {
        background-color: white !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 576px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

/* تخصيصات للنماذج المنبثقة */
.modal-content {
    border-radius: 0.75rem;
    border: none;
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
    background-color: #f8f9fc;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    background-color: #f8f9fc;
}

/* تحسينات للوصولية */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تخصيصات للتركيز */
.form-control:focus,
.btn:focus,
.nav-link:focus {
    outline: 2px solid #4e73df;
    outline-offset: 2px;
}
