<?php
/**
 * سكريبت النسخ الاحتياطي
 * نظام ERP المحاسبي
 */

require_once __DIR__ . '/../config/database.php';

class BackupManager {
    private $db;
    private $backupPath;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->backupPath = __DIR__ . '/../backups/';
        
        // إنشاء مجلد النسخ الاحتياطي
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }
    
    /**
     * إنشاء نسخة احتياطية كاملة
     */
    public function createFullBackup() {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "backup_full_{$timestamp}.sql";
        $filepath = $this->backupPath . $filename;
        
        echo "إنشاء نسخة احتياطية كاملة...\n";
        echo "الملف: $filename\n";
        
        try {
            $this->exportDatabase($filepath);
            $this->compressBackup($filepath);
            
            // حفظ معلومات النسخة الاحتياطية
            $this->logBackup($filename, 'full', filesize($filepath . '.gz'));
            
            echo "✓ تم إنشاء النسخة الاحتياطية بنجاح\n";
            echo "الحجم: " . $this->formatFileSize(filesize($filepath . '.gz')) . "\n";
            
            // حذف الملف غير المضغوط
            unlink($filepath);
            
            return $filepath . '.gz';
            
        } catch (Exception $e) {
            echo "✗ فشل في إنشاء النسخة الاحتياطية: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * إنشاء نسخة احتياطية للبيانات فقط
     */
    public function createDataBackup() {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "backup_data_{$timestamp}.sql";
        $filepath = $this->backupPath . $filename;
        
        echo "إنشاء نسخة احتياطية للبيانات...\n";
        echo "الملف: $filename\n";
        
        try {
            $this->exportDatabase($filepath, true);
            $this->compressBackup($filepath);
            
            $this->logBackup($filename, 'data', filesize($filepath . '.gz'));
            
            echo "✓ تم إنشاء النسخة الاحتياطية بنجاح\n";
            echo "الحجم: " . $this->formatFileSize(filesize($filepath . '.gz')) . "\n";
            
            unlink($filepath);
            
            return $filepath . '.gz';
            
        } catch (Exception $e) {
            echo "✗ فشل في إنشاء النسخة الاحتياطية: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * تصدير قاعدة البيانات
     */
    private function exportDatabase($filepath, $dataOnly = false) {
        $output = fopen($filepath, 'w');
        
        if (!$output) {
            throw new Exception("لا يمكن إنشاء ملف النسخة الاحتياطية");
        }
        
        // كتابة رأس الملف
        fwrite($output, "-- نسخة احتياطية لقاعدة البيانات\n");
        fwrite($output, "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n");
        fwrite($output, "-- قاعدة البيانات: " . DB_NAME . "\n");
        fwrite($output, "-- نوع النسخة: " . ($dataOnly ? 'بيانات فقط' : 'كاملة') . "\n\n");
        
        fwrite($output, "SET FOREIGN_KEY_CHECKS=0;\n");
        fwrite($output, "SET SQL_MODE='NO_AUTO_VALUE_ON_ZERO';\n");
        fwrite($output, "SET AUTOCOMMIT=0;\n");
        fwrite($output, "START TRANSACTION;\n\n");
        
        // الحصول على قائمة الجداول
        $tables = $this->getTables();
        
        foreach ($tables as $table) {
            echo "  معالجة الجدول: $table\n";
            
            if (!$dataOnly) {
                // تصدير هيكل الجدول
                $this->exportTableStructure($output, $table);
            }
            
            // تصدير بيانات الجدول
            $this->exportTableData($output, $table);
        }
        
        fwrite($output, "\nSET FOREIGN_KEY_CHECKS=1;\n");
        fwrite($output, "COMMIT;\n");
        
        fclose($output);
    }
    
    /**
     * الحصول على قائمة الجداول
     */
    private function getTables() {
        $stmt = $this->db->query("SHOW TABLES");
        $tables = [];
        
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }
        
        return $tables;
    }
    
    /**
     * تصدير هيكل الجدول
     */
    private function exportTableStructure($output, $table) {
        fwrite($output, "-- هيكل الجدول `$table`\n");
        fwrite($output, "DROP TABLE IF EXISTS `$table`;\n");
        
        $stmt = $this->db->query("SHOW CREATE TABLE `$table`");
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        fwrite($output, $row['Create Table'] . ";\n\n");
    }
    
    /**
     * تصدير بيانات الجدول
     */
    private function exportTableData($output, $table) {
        $stmt = $this->db->query("SELECT * FROM `$table`");
        $rowCount = 0;
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if ($rowCount === 0) {
                fwrite($output, "-- بيانات الجدول `$table`\n");
                
                $columns = array_keys($row);
                $columnsList = '`' . implode('`, `', $columns) . '`';
                fwrite($output, "INSERT INTO `$table` ($columnsList) VALUES\n");
            }
            
            $values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $values[] = 'NULL';
                } else {
                    $values[] = "'" . $this->db->quote($value) . "'";
                }
            }
            
            $comma = ($rowCount > 0) ? ',' : '';
            fwrite($output, "$comma(" . implode(', ', $values) . ")\n");
            
            $rowCount++;
        }
        
        if ($rowCount > 0) {
            fwrite($output, ";\n\n");
        }
    }
    
    /**
     * ضغط النسخة الاحتياطية
     */
    private function compressBackup($filepath) {
        if (function_exists('gzencode')) {
            $data = file_get_contents($filepath);
            $compressed = gzencode($data, 9);
            file_put_contents($filepath . '.gz', $compressed);
        }
    }
    
    /**
     * تسجيل معلومات النسخة الاحتياطية
     */
    private function logBackup($filename, $type, $size) {
        try {
            // إنشاء جدول سجل النسخ الاحتياطي
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS backup_log (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    filename VARCHAR(255) NOT NULL,
                    type ENUM('full', 'data', 'structure') NOT NULL,
                    size BIGINT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
            
            $stmt = $this->db->prepare("
                INSERT INTO backup_log (filename, type, size) 
                VALUES (?, ?, ?)
            ");
            
            $stmt->execute([$filename, $type, $size]);
            
        } catch (Exception $e) {
            // تجاهل أخطاء التسجيل
        }
    }
    
    /**
     * حذف النسخ الاحتياطية القديمة
     */
    public function cleanOldBackups($days = 30) {
        echo "حذف النسخ الاحتياطية الأقدم من $days يوم...\n";
        
        $files = glob($this->backupPath . 'backup_*.sql.gz');
        $deleted = 0;
        
        foreach ($files as $file) {
            $fileTime = filemtime($file);
            $daysDiff = (time() - $fileTime) / (24 * 60 * 60);
            
            if ($daysDiff > $days) {
                unlink($file);
                $deleted++;
                echo "  حذف: " . basename($file) . "\n";
            }
        }
        
        echo "تم حذف $deleted ملف\n";
    }
    
    /**
     * عرض قائمة النسخ الاحتياطية
     */
    public function listBackups() {
        echo "النسخ الاحتياطية المتوفرة:\n";
        echo str_repeat("-", 80) . "\n";
        printf("%-30s %-10s %-15s %-20s\n", "اسم الملف", "النوع", "الحجم", "التاريخ");
        echo str_repeat("-", 80) . "\n";
        
        $files = glob($this->backupPath . 'backup_*.sql.gz');
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        foreach ($files as $file) {
            $filename = basename($file);
            $size = $this->formatFileSize(filesize($file));
            $date = date('Y-m-d H:i:s', filemtime($file));
            
            // تحديد نوع النسخة من اسم الملف
            if (strpos($filename, '_full_') !== false) {
                $type = 'كاملة';
            } elseif (strpos($filename, '_data_') !== false) {
                $type = 'بيانات';
            } else {
                $type = 'غير محدد';
            }
            
            printf("%-30s %-10s %-15s %-20s\n", $filename, $type, $size, $date);
        }
    }
    
    /**
     * تنسيق حجم الملف
     */
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

// معالجة الأوامر
if (php_sapi_name() === 'cli') {
    $action = $argv[1] ?? 'full';
    
    $backup = new BackupManager();
    
    switch ($action) {
        case 'full':
            $backup->createFullBackup();
            break;
            
        case 'data':
            $backup->createDataBackup();
            break;
            
        case 'list':
            $backup->listBackups();
            break;
            
        case 'clean':
            $days = isset($argv[2]) ? (int)$argv[2] : 30;
            $backup->cleanOldBackups($days);
            break;
            
        default:
            echo "الاستخدام:\n";
            echo "  php backup.php full     - نسخة احتياطية كاملة\n";
            echo "  php backup.php data     - نسخة احتياطية للبيانات فقط\n";
            echo "  php backup.php list     - عرض النسخ الاحتياطية\n";
            echo "  php backup.php clean [days] - حذف النسخ القديمة\n";
    }
} else {
    echo "هذا السكريبت يعمل من سطر الأوامر فقط\n";
}
?>
